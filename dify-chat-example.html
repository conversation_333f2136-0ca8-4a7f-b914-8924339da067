<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dify Chat 组件示例</title>
  <style>
    .integration-example {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      height: 100vh;
      width: 100vw;
    }

    body {
      height: 100vh;
      width: 100vw;
      margin: 0;
      padding: 0;
    }
  </style>
</head>

<body>
  <div id="demo-app">
    <div class="integration-example">
      <dify-chat api-url="http://*************:8080/v1" api-key="app-lrt9TpLGzouDkbx8HBfJDgDc" user="8392EA9256FD4788B3D27ED76F707323" 
        post-name="前端工程师"></dify-chat>
    </div>
  </div>

  <!-- 引入Vue2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>

  <!-- 引入Dify Chat组件 -->
  <script src="lib/dify-chat.js"></script>

  <script>
    // 初始化Vue应用
    new Vue({
      el: '#demo-app',
      methods: {
        handleMessageSent(message) {
          console.log('用户发送消息:', message);
        },
        handleChatOpened() {
          console.log('聊天窗口已打开');
        }
      }
    });
  </script>
</body>

</html>